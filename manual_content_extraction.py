#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于已有的OCR结果和文档内容，手动整理各个工具的详细信息
"""

def get_funding_tools_info():
    """资金拨付工具信息"""
    return {
        "name": "资金拨付工具",
        "description": "通过移动支付方式进行公益资助拨款的数字化工具",
        "features": [
            "移动支付方式拨款",
            "受益人实时领取",
            "自动记录拨付数据",
            "支持批量操作",
            "资金流向透明化"
        ],
        "scenarios": [
            "教育助学金发放",
            "医疗救助金拨付", 
            "困难家庭生活补助",
            "学习用品采购资金",
            "紧急救助资金发放"
        ],
        "requirements": [
            "单个受益人拨款金额≥10元",
            "受益人须完成拨款领取",
            "仅限教育助学类项目",
            "仅适用于受益人善款拨付"
        ],
        "funding": {
            "calculation": "有效受益人数 × 10元",
            "max_amount": "25,000元",
            "note": "衡量周期内对受益人执行资金拨付多次，不重复计算"
        }
    }

def get_material_tools_info():
    """物资执行工具信息"""
    return {
        "name": "物资执行工具",
        "description": "仓配一体化物资发放管理工具",
        "features": [
            "平台入仓管理",
            "统一配送服务",
            "物流轨迹跟踪",
            "收货确认机制",
            "成本核算统计"
        ],
        "scenarios": [
            "图书教材配送",
            "学习用品发放",
            "志愿者回礼寄送",
            "捐赠证书邮寄",
            "公益物资配送"
        ],
        "requirements": [
            "公益物资须通过平台入仓并配送",
            "上门取件模式不纳入资助范围",
            "包括对受助对象、志愿者、捐赠人发放物资",
            "机构内部物品物流费用不纳入资助"
        ],
        "funding": {
            "calculation": "根据平台实际产生的物流费用进行资助",
            "max_amount": "80,000元",
            "note": "需提供发票和仓储物流结算明细单"
        }
    }

def get_service_tools_info():
    """服务执行工具信息"""
    return {
        "name": "服务执行工具",
        "description": "标准化服务任务管理工具，支持'计件化'服务执行",
        "features": [
            "标准计件化管理",
            "任务下发与跟踪",
            "执行过程记录",
            "实时反馈机制",
            "数据自动沉淀"
        ],
        "scenarios": [
            "课堂教学服务",
            "家访陪伴服务", 
            "设备安装维护",
            "心理辅导服务",
            "上门服务类"
        ],
        "service_types": {
            "课堂类": "线上/线下教学活动",
            "家访类": "入户走访服务",
            "陪伴类": "心理陪伴支持",
            "安装类": "设备运输、安装、跟踪",
            "心理类": "心理咨询辅导",
            "上门服务类": "各类上门服务"
        },
        "funding": {
            "calculation": "资助系数 × 有效执行受助对象数量 × 5元",
            "max_amount": "50,000元",
            "coefficient_formula": "服务执行场景系数 + 人群系数 + 服务落地场景系数",
            "coefficients": {
                "服务执行场景系数": 1,
                "人群系数": {"个人": 0.3, "群体": 0.5},
                "服务落地场景系数": {"线下": 0.3, "线上": 0.1}
            },
            "example": "课堂类，群体对象40人，线上场景：(1+0.5+0.1)×40×5=320元"
        }
    }

def get_private_domain_tools_info():
    """私域运营工具信息"""
    return {
        "name": "私域运营工具",
        "description": "企微私域运营和腾讯频道运营工具",
        "components": {
            "企微私域": {
                "features": [
                    "组织认证与通讯录建立",
                    "小助手账号设立",
                    "用户标签管理",
                    "群发消息功能",
                    "朋友圈运营",
                    "离职人员管理",
                    "内容审计拦截"
                ],
                "requirements": [
                    "完成组织认证",
                    "建立组织通讯录",
                    "设立小助手账号",
                    "每月至少1次活动",
                    "每月至少4次素材发布"
                ]
            },
            "腾讯频道": {
                "features": [
                    "多角色交流互动",
                    "内容沉淀不过期",
                    "与公益平台打通",
                    "与微信生态联动",
                    "活动组织管理"
                ],
                "requirements": [
                    "频道人数超过100人",
                    "每周至少3条发帖",
                    "发帖人数占比超过10%",
                    "平均每帖互动数超过5个"
                ]
            }
        },
        "scenarios": [
            "月捐用户管理",
            "多渠道触达服务",
            "运营数据分析",
            "频道内容运营",
            "志愿者社群管理",
            "捐赠人服务"
        ],
        "funding": {
            "calculation": "运营人员费用，4000元/月",
            "max_amount": "24,000元",
            "duration": "6个月",
            "requirements": "前述两项月度数据均达标"
        }
    }

def get_donor_service_info():
    """捐赠人服务工具信息"""
    return {
        "name": "捐赠人服务工具",
        "description": "月捐用户管理和服务工具",
        "features": [
            "用户信息授权管理",
            "月捐用户管理",
            "触达服务设置",
            "标签管理系统",
            "短信服务功能",
            "一起月捐功能"
        ],
        "user_management": [
            "用户信息授权（前置/后置收集）",
            "月捐金额、时长管理",
            "用户标签管理",
            "捐赠明细查看",
            "服务记录管理"
        ],
        "touch_services": [
            "月捐感谢页",
            "短信触达",
            "邮件服务",
            "微信模板消息",
            "节点服务设置"
        ],
        "scenarios": [
            "月捐劝募",
            "用户留存",
            "感谢服务",
            "定期沟通",
            "数据分析"
        ]
    }

def main():
    """输出所有工具的详细信息"""
    tools = [
        get_funding_tools_info(),
        get_material_tools_info(), 
        get_service_tools_info(),
        get_private_domain_tools_info(),
        get_donor_service_info()
    ]
    
    print("=== 腾讯公益数字化工具详细信息 ===\n")
    
    for i, tool in enumerate(tools, 1):
        print(f"{i}. {tool['name']}")
        print("=" * 50)
        print(f"描述：{tool['description']}")
        
        if 'features' in tool:
            print(f"\n核心功能：")
            for feature in tool['features']:
                print(f"  • {feature}")
        
        if 'scenarios' in tool:
            print(f"\n应用场景：")
            for scenario in tool['scenarios']:
                print(f"  • {scenario}")
        
        if 'requirements' in tool:
            print(f"\n使用要求：")
            for req in tool['requirements']:
                print(f"  • {req}")
        
        if 'funding' in tool:
            print(f"\n资助标准：")
            funding = tool['funding']
            print(f"  • 计算方式：{funding['calculation']}")
            print(f"  • 最高金额：{funding['max_amount']}")
            if 'note' in funding:
                print(f"  • 备注：{funding['note']}")
        
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()
