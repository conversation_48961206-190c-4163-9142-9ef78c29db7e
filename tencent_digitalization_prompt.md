# 腾讯公益数字化资助项目推广策略

## 项目背景

你现在负责**上海联劝公益基金会**的数字化工作，承接了向腾讯公益申请的数字化资助项目。

### 关键信息
- **资助机制**：资助金额与工具使用数据正相关，使用量越大，资助金额越多
- **目标**：号召业务团队积极参与，最大化工具使用数据
- **受众**：基金会全体业务团队成员
- **时间**：当前时间为6月30日，激励计划的结算时间为8月31日，但腾讯有可能会延长时间
- **联劝**：上海联劝公益基金会的理念核心包括以捐赠人为中心。

## 任务要求

### 任务一：全员例会介绍网页

创建一个完整的网页，用于在全员例会上介绍此计划，需要包含：

#### 内容要求
- **项目介绍**：清晰阐述数字化资助项目的价值和意义
- **工具展示**：全面介绍腾讯提供的各类数字化工具（不包括资金拨付工具）
- **业务场景**：从业务视角出发，详细说明每个工具的实际应用场景
- **价值体现**：明确展示使用工具后能够产生的具体业务价值
- **激励机制**：说明使用数据与资助金额的关联关系

#### 设计要求
- **界面友好**：包含工具的实际界面截图和演示（切记不能直接拿长截图作为演示）
- **交互设计**：用户体验友好，操作直观
- **视觉突出**：重点信息突出显示，层次分明
- **逻辑清晰**：信息组织有序，便于理解
- **说服力强**：能够有效激发团队参与积极性

### 任务二：工具操作指南网页

创建一个操作指南网页，帮助业务同事快速上手：

#### 功能要求
- **快速入门**：提供简洁明了的操作流程
- **分步指导**：详细的step-by-step操作说明
- **场景示例**：结合实际业务场景的使用案例
- **常见问题**：FAQ部分解决常见疑问
- **技能速成**：让业务同事能够轻松掌握工具使用

#### 用户体验要求
- **交互友好**：界面设计简洁易懂
- **导航清晰**：快速定位所需信息
- **移动适配**：支持多终端访问
- **搜索功能**：便于查找特定工具或功能

## 参考资料

### 必需参考内容
1. **资助金额计算方法**
   - 文件：捐赠协议附件1
   - 用途：了解具体的资助金额计算规则和激励机制

2. **工具使用方法**
   - 文件：目录下各PNG格式长截图
   - 用途：识别并理解各工具的具体操作流程和界面设计

3. **补充资料**
   - 文件：目录下PDF、PPT、DOCX等格式文件
   - 用途：获取项目背景、工具详情等补充信息

### 内容分析要求
- 仔细识别图片中的界面元素、操作步骤和功能说明
- 提取关键的业务场景和使用价值点
- 理解资助金额与使用数据的具体关联关系
- 分析目标用户（业务团队）的需求和痛点

## 输出要求

### 格式标准
- 提供完整的HTML网页代码
- 包含CSS样式设计
- 确保响应式布局
- 添加必要的JavaScript交互功能

### 质量标准
- 内容准确、完整
- 设计美观、专业
- 用户体验优良
- 逻辑结构清晰
- 具有较强的说服力和实用性

## 成功指标

- 业务团队对项目的理解度和参与意愿
- 工具使用数据的增长情况
- 团队操作熟练度的提升速度
- 最终获得的资助金额达成情况