#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from docx import Document
from pptx import Presentation
import sys
from PIL import Image
import pytesseract

def parse_docx(file_path):
    """解析Word文档"""
    try:
        doc = Document(file_path)
        content = []
        
        print(f"\n=== 解析Word文档: {file_path} ===")
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        # 解析表格
        for table in doc.tables:
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_data.append(cell.text.strip())
                if row_data:
                    content.append(" | ".join(row_data))
        
        return content
    except Exception as e:
        print(f"解析Word文档时出错: {e}")
        return []

def parse_pptx(file_path):
    """解析PowerPoint文档"""
    try:
        prs = Presentation(file_path)
        content = []
        
        print(f"\n=== 解析PPT文档: {file_path} ===")
        
        for i, slide in enumerate(prs.slides, 1):
            slide_content = [f"\n--- 幻灯片 {i} ---"]
            
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    slide_content.append(shape.text.strip())
                
                # 处理表格
                if shape.has_table:
                    table = shape.table
                    for row in table.rows:
                        row_data = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_data.append(cell.text.strip())
                        if row_data:
                            slide_content.append(" | ".join(row_data))
            
            content.extend(slide_content)
        
        return content
    except Exception as e:
        print(f"解析PPT文档时出错: {e}")
        return []

def parse_image_ocr(file_path):
    """使用OCR解析图片中的文字"""
    try:
        print(f"\n=== OCR解析图片: {file_path} ===")

        # 打开图片
        image = Image.open(file_path)

        # 获取图片尺寸
        width, height = image.size
        print(f"图片尺寸: {width} x {height}")

        # 如果图片太大，进行分割处理
        if width > 2000 or height > 10000:
            print("图片过大，进行分割处理...")
            return parse_large_image_in_chunks(image, file_path)

        # 图片预处理
        processed_image = preprocess_image(image)

        # 使用多种OCR配置尝试识别
        text_results = []

        # 配置1: 中英文混合，默认设置
        try:
            text1 = pytesseract.image_to_string(processed_image, lang='chi_sim+eng',
                                               config='--oem 3 --psm 6')
            text_results.append(text1)
        except:
            pass

        # 配置2: 更精确的文本检测
        try:
            text2 = pytesseract.image_to_string(processed_image, lang='chi_sim+eng',
                                               config='--oem 3 --psm 4')
            text_results.append(text2)
        except:
            pass

        # 配置3: 单列文本
        try:
            text3 = pytesseract.image_to_string(processed_image, lang='chi_sim+eng',
                                               config='--oem 3 --psm 8')
            text_results.append(text3)
        except:
            pass

        # 选择最长的结果作为最终结果
        best_text = max(text_results, key=len) if text_results else ""

        # 清理和分割文本
        lines = clean_text_lines(best_text)

        return lines
    except Exception as e:
        print(f"OCR解析图片时出错: {e}")
        return []

def preprocess_image(image):
    """图片预处理以提高OCR准确性"""
    from PIL import ImageEnhance, ImageFilter

    # 转换为RGB模式
    if image.mode != 'RGB':
        image = image.convert('RGB')

    # 增强对比度
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(1.3)

    # 增强锐度
    enhancer = ImageEnhance.Sharpness(image)
    image = enhancer.enhance(1.1)

    # 轻微降噪
    try:
        image = image.filter(ImageFilter.MedianFilter(size=3))
    except:
        pass

    return image

def parse_large_image_in_chunks(image, filename):
    """分块处理大图片"""
    width, height = image.size
    chunk_height = 5000  # 每块的高度
    overlap = 200  # 重叠区域，避免文字被截断

    all_lines = []
    print(f"开始分块处理大图片: {filename}")

    for y in range(0, height, chunk_height - overlap):
        # 计算当前块的边界
        top = max(0, y - overlap if y > 0 else 0)
        bottom = min(height, y + chunk_height)

        # 裁剪图片块
        chunk = image.crop((0, top, width, bottom))

        print(f"处理图片块: y={top}-{bottom}")

        try:
            # 预处理图片块
            processed_chunk = preprocess_image(chunk)

            # OCR识别
            text = pytesseract.image_to_string(processed_chunk, lang='chi_sim+eng',
                                             config='--oem 3 --psm 6')

            # 清理文本
            chunk_lines = clean_text_lines(text)
            all_lines.extend(chunk_lines)

        except Exception as e:
            print(f"处理图片块时出错: {e}")
            continue

    # 去重相似的行（由于重叠区域可能产生重复）
    return remove_duplicate_lines(all_lines)

def clean_text_lines(text):
    """清理和优化文本行"""
    if not text:
        return []

    lines = []
    for line in text.split('\n'):
        line = line.strip()
        if not line:
            continue

        # 基本清理
        line = line.replace('  ', ' ')  # 多个空格替换为单个空格

        # 过滤掉过短或明显错误的行
        if len(line) < 2:
            continue

        # 过滤掉只包含特殊字符的行
        if all(not c.isalnum() and not c.isspace() for c in line):
            continue

        lines.append(line)

    return lines

def remove_duplicate_lines(lines):
    """移除重复或高度相似的行"""
    if not lines:
        return []

    unique_lines = []
    seen = set()

    for line in lines:
        # 简单的去重：基于内容的前20个字符
        key = line[:20].lower().strip()
        if key not in seen:
            seen.add(key)
            unique_lines.append(line)

    return unique_lines

def main():
    # 获取当前目录下的文档文件
    current_dir = "."

    # 解析Word文档
    docx_files = [f for f in os.listdir(current_dir) if f.endswith('.docx')]
    for docx_file in docx_files:
        content = parse_docx(docx_file)
        if content:
            print("\n内容:")
            for line in content:
                print(line)

    # 解析PPT文档
    pptx_files = [f for f in os.listdir(current_dir) if f.endswith('.pptx')]
    for pptx_file in pptx_files:
        content = parse_pptx(pptx_file)
        if content:
            print("\n内容:")
            for line in content:
                print(line)

    # 解析PNG图片文件
    png_files = [f for f in os.listdir(current_dir) if f.endswith('.png')]
    for png_file in png_files:
        content = parse_image_ocr(png_file)
        if content:
            print("\n内容:")
            for line in content:
                print(line)

if __name__ == "__main__":
    main()
