#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import cv2
import numpy as np

def enhance_image_for_ocr(image):
    """增强图片以提高OCR识别率"""
    # 转换为numpy数组
    img_array = np.array(image)
    
    # 转换为灰度图
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array
    
    # 应用高斯模糊去噪
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    
    # 自适应阈值处理
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    
    # 形态学操作去除噪点
    kernel = np.ones((2,2), np.uint8)
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    # 转换回PIL图像
    enhanced_image = Image.fromarray(cleaned)
    
    return enhanced_image

def process_large_image_advanced(image_path):
    """高级处理大图片"""
    print(f"\n=== 高级OCR处理: {image_path} ===")
    
    try:
        # 打开图片
        image = Image.open(image_path)
        width, height = image.size
        print(f"原始图片尺寸: {width} x {height}")
        
        # 如果图片过大，先缩放到合适尺寸
        max_dimension = 4000
        if width > max_dimension or height > max_dimension:
            ratio = min(max_dimension/width, max_dimension/height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            print(f"缩放后尺寸: {new_width} x {new_height}")
        
        # 图片增强
        enhanced_image = enhance_image_for_ocr(image)
        
        # 多种OCR配置尝试
        ocr_configs = [
            '--oem 3 --psm 6',  # 统一文本块
            '--oem 3 --psm 4',  # 单列文本
            '--oem 3 --psm 3',  # 完全自动页面分割
            '--oem 3 --psm 1',  # 自动页面分割与OSD
        ]
        
        best_result = ""
        best_length = 0
        
        for config in ocr_configs:
            try:
                print(f"尝试配置: {config}")
                text = pytesseract.image_to_string(enhanced_image, lang='chi_sim+eng', config=config)
                
                if len(text) > best_length:
                    best_result = text
                    best_length = len(text)
                    print(f"当前最佳结果长度: {best_length}")
                    
            except Exception as e:
                print(f"配置 {config} 失败: {e}")
                continue
        
        # 如果结果仍然不理想，尝试分块处理
        if best_length < 1000:  # 如果识别结果太少，尝试分块
            print("尝试分块处理...")
            chunk_result = process_image_in_chunks(enhanced_image)
            if len(chunk_result) > len(best_result):
                best_result = chunk_result
        
        # 清理文本
        lines = clean_and_format_text(best_result)
        
        return lines
        
    except Exception as e:
        print(f"处理图片时出错: {e}")
        return []

def process_image_in_chunks(image):
    """分块处理图片"""
    width, height = image.size
    chunk_height = 2000
    overlap = 100
    
    all_text = []
    
    for y in range(0, height, chunk_height - overlap):
        top = max(0, y - overlap if y > 0 else 0)
        bottom = min(height, y + chunk_height)
        
        chunk = image.crop((0, top, width, bottom))
        
        try:
            text = pytesseract.image_to_string(chunk, lang='chi_sim+eng', 
                                             config='--oem 3 --psm 6')
            all_text.append(text)
        except:
            continue
    
    return '\n'.join(all_text)

def clean_and_format_text(text):
    """清理和格式化文本"""
    if not text:
        return []
    
    lines = []
    for line in text.split('\n'):
        line = line.strip()
        if not line:
            continue
        
        # 基本清理
        line = ' '.join(line.split())  # 规范化空格
        
        # 过滤明显的错误识别
        if len(line) < 2:
            continue
        
        # 过滤只包含特殊字符的行
        if not any(c.isalnum() for c in line):
            continue
        
        lines.append(line)
    
    return lines

def main():
    """主函数"""
    current_dir = "."
    
    # 获取所有PNG文件
    png_files = [f for f in os.listdir(current_dir) if f.endswith('.png')]
    
    for png_file in png_files:
        print(f"\n{'='*60}")
        print(f"处理文件: {png_file}")
        print('='*60)
        
        lines = process_large_image_advanced(png_file)
        
        if lines:
            print(f"\n识别到 {len(lines)} 行文本:")
            print("-" * 40)
            for i, line in enumerate(lines[:50], 1):  # 只显示前50行
                print(f"{i:3d}: {line}")
            
            if len(lines) > 50:
                print(f"... 还有 {len(lines) - 50} 行文本")
        else:
            print("未识别到有效文本")

if __name__ == "__main__":
    main()
