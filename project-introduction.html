<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>善育50·数字赋能计划 - 项目介绍</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .highlight-box h3 {
            color: #d63384;
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .funding-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .funding-item {
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: white;
        }

        .funding-item h4 {
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .funding-amount {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        .tools-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .tool-item {
            background: white;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tool-item h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .scenario-list {
            list-style: none;
            padding-left: 0;
        }

        .scenario-list li {
            background: #e3f2fd;
            margin: 8px 0;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 3px solid #2196f3;
        }

        .cta-section {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            text-align: center;
            padding: 40px;
            border-radius: 15px;
            margin-top: 30px;
        }

        .cta-section h2 {
            color: white;
            border: none;
            margin-bottom: 20px;
        }

        .btn {
            display: inline-block;
            background: white;
            color: #ff6b6b;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            width: 45%;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .timeline-item:nth-child(odd) {
            left: 0;
        }

        .timeline-item:nth-child(even) {
            left: 55%;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 20px;
            width: 15px;
            height: 15px;
            background: #667eea;
            border-radius: 50%;
        }

        .timeline-item:nth-child(odd)::before {
            right: -32px;
        }

        .timeline-item:nth-child(even)::before {
            left: -32px;
        }

        .calculation-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .calc-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .calc-box {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            border-left: 4px solid #28a745;
        }

        .requirements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 20px 0;
        }

        .req-list {
            list-style: none;
            padding: 0;
        }

        .req-list li {
            background: #e8f5e8;
            margin: 8px 0;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }

        .req-list li::before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }

        .warning-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            margin: 20px 0;
            color: #721c24;
        }

        .warning-box h3 {
            color: #721c24;
            margin-bottom: 15px;
        }

        .highlight-text {
            font-size: 1.1em;
            font-weight: bold;
            background: rgba(255,255,255,0.3);
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tip-item {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .tip-item h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .cta-buttons {
            margin-top: 20px;
        }

        .cta-buttons .btn {
            margin: 10px 15px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }

            .funding-grid {
                grid-template-columns: 1fr;
            }

            .calculation-examples {
                grid-template-columns: 1fr;
            }

            .requirements-grid {
                grid-template-columns: 1fr;
            }

            .tips-grid {
                grid-template-columns: 1fr;
            }

            .timeline::before {
                left: 20px;
            }

            .timeline-item {
                width: calc(100% - 40px);
                left: 40px !important;
            }

            .timeline-item::before {
                left: -32px !important;
            }

            .cta-buttons .btn {
                display: block;
                margin: 10px auto;
                width: 80%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>善育50·数字赋能计划</h1>
            <p class="subtitle">教育助学数字化升级资助项目</p>
            <p class="subtitle">海南亚洲公益研究院 × 上海联劝公益基金会</p>
        </div>

        <div class="card">
            <h2>🎯 项目核心价值</h2>
            <div class="highlight-box">
                <h3>数字化转型 × 资助激励 = 双重收益</h3>
                <p>通过数字化工具提升项目执行效率，同时获得最高<strong>17.9万元</strong>的资助支持</p>
            </div>
            <p>在数字化时代，公益行业正通过数字化手段全面提升项目运行效率、信息处理能力和反馈能力，为受助人、志愿者、捐赠人提供更好的服务。数字化已成为公益机构提升效能、增强公信力的关键路径。</p>
        </div>

        <div class="card">
            <h2>💰 四大资助方向</h2>
            <div class="funding-grid">
                <div class="funding-item">
                    <h4>资金拨付数字化</h4>
                    <div class="funding-amount">2.5万元</div>
                    <p>有效受益人数 × 10元</p>
                    <small>单个受益人拨款≥10元</small>
                </div>
                <div class="funding-item">
                    <h4>物资执行数字化</h4>
                    <div class="funding-amount">8万元</div>
                    <p>实际仓储物流费用</p>
                    <small>需通过平台入仓配送</small>
                </div>
                <div class="funding-item">
                    <h4>服务执行数字化</h4>
                    <div class="funding-amount">5万元</div>
                    <p>资助系数 × 受助对象数 × 5元</p>
                    <small>支持多种服务场景</small>
                </div>
                <div class="funding-item">
                    <h4>私域运营资助</h4>
                    <div class="funding-amount">2.4万元</div>
                    <p>4000元/月 × 6个月</p>
                    <small>运营人员费用支持</small>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🛠️ 数字化工具全景</h2>
            <div class="tools-section">
                <div class="tool-item">
                    <h4>📱 资金拨付工具</h4>
                    <p><strong>核心功能：</strong>移动支付方式进行公益资助拨款</p>
                    <ul class="scenario-list">
                        <li>🎓 教育助学金发放</li>
                        <li>💊 医疗救助金拨付</li>
                        <li>🏠 困难家庭生活补助</li>
                        <li>📚 学习用品采购资金</li>
                    </ul>
                </div>
                
                <div class="tool-item">
                    <h4>📦 物资执行工具</h4>
                    <p><strong>核心功能：</strong>仓配一体化物资发放管理</p>
                    <ul class="scenario-list">
                        <li>📚 图书教材配送</li>
                        <li>🎒 学习用品发放</li>
                        <li>🏆 志愿者回礼寄送</li>
                        <li>📜 捐赠证书邮寄</li>
                    </ul>
                </div>
                
                <div class="tool-item">
                    <h4>🎯 服务执行工具</h4>
                    <p><strong>核心功能：</strong>标准化服务任务管理</p>
                    <ul class="scenario-list">
                        <li>👨‍🏫 课堂教学服务</li>
                        <li>🏠 家访陪伴服务</li>
                        <li>💻 设备安装维护</li>
                        <li>💝 心理辅导服务</li>
                    </ul>
                </div>
                
                <div class="tool-item">
                    <h4>📢 私域运营工具</h4>
                    <p><strong>核心功能：</strong>捐赠人服务与社群运营</p>
                    <ul class="scenario-list">
                        <li>👥 月捐用户管理</li>
                        <li>📧 多渠道触达服务</li>
                        <li>📊 运营数据分析</li>
                        <li>🎪 频道内容运营</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>⏰ 项目时间轴</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <h4>现在 - 8月15日</h4>
                    <p>滚动申请期</p>
                    <small>随时可以提交申请材料</small>
                </div>
                <div class="timeline-item">
                    <h4>3月1日 - 8月31日</h4>
                    <p>执行衡量周期</p>
                    <small>工具使用数据统计期间</small>
                </div>
                <div class="timeline-item">
                    <h4>项目结束后</h4>
                    <p>验收与拨付</p>
                    <small>提交验收材料，审核通过后拨付资助</small>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📊 资助计算详解</h2>
            <div class="calculation-examples">
                <div class="calc-item">
                    <h4>💰 资金拨付示例</h4>
                    <div class="calc-box">
                        <p><strong>场景：</strong>为100名学生发放助学金，每人200元</p>
                        <p><strong>计算：</strong>100人 × 10元 = 1,000元资助</p>
                        <p><strong>收益：</strong>发放20,000元助学金，获得1,000元运营资助</p>
                    </div>
                </div>

                <div class="calc-item">
                    <h4>📦 物资执行示例</h4>
                    <div class="calc-box">
                        <p><strong>场景：</strong>为500名学生配送学习用品</p>
                        <p><strong>物流费：</strong>实际产生3,000元仓储配送费</p>
                        <p><strong>资助：</strong>100%报销，获得3,000元资助</p>
                    </div>
                </div>

                <div class="calc-item">
                    <h4>🎯 服务执行示例</h4>
                    <div class="calc-box">
                        <p><strong>场景：</strong>线下课堂教学，群体对象40人</p>
                        <p><strong>系数：</strong>1(课堂) + 0.5(群体) + 0.3(线下) = 1.8</p>
                        <p><strong>计算：</strong>1.8 × 40人 × 5元 = 360元</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🎖️ 申报条件与优势</h2>
            <div class="requirements-grid">
                <div class="req-column">
                    <h4>✅ 基本条件</h4>
                    <ul class="req-list">
                        <li>业务范围含教育、儿童青少年发展</li>
                        <li>依法登记满一年，证书有效</li>
                        <li>具有公募资质</li>
                        <li>有免税资质，3A评级优先</li>
                        <li>已入驻腾讯公益等平台</li>
                        <li>近一年年检合格，无违法记录</li>
                    </ul>
                </div>
                <div class="req-column">
                    <h4>💪 能力要求</h4>
                    <ul class="req-list">
                        <li>有稳定的项目执行团队</li>
                        <li>积极主动推动数字化发展</li>
                        <li>将数字化作为重要发展方向</li>
                        <li>有意愿学习使用新工具</li>
                    </ul>

                    <h4 style="margin-top: 20px;">🏆 联劝优势</h4>
                    <ul class="req-list">
                        <li>✅ 公募资质完备</li>
                        <li>✅ 3A级社会组织</li>
                        <li>✅ 丰富项目执行经验</li>
                        <li>✅ 以捐赠人为中心理念</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>⚠️ 重要提醒</h2>
            <div class="warning-box">
                <h3>🔥 抓住时间窗口</h3>
                <p>执行衡量周期：<strong>2025年3月1日 - 8月31日</strong></p>
                <p>申请截止时间：<strong>2025年8月15日</strong></p>
                <p class="highlight-text">时间紧迫，越早开始使用工具，累积数据越多，获得资助越丰厚！</p>
            </div>

            <div class="tips-grid">
                <div class="tip-item">
                    <h4>💡 成功秘诀</h4>
                    <p>将现有项目执行流程数字化，既提升效率又获得资助</p>
                </div>
                <div class="tip-item">
                    <h4>📈 数据为王</h4>
                    <p>工具使用数据直接决定资助金额，多用多得</p>
                </div>
                <div class="tip-item">
                    <h4>🤝 团队协作</h4>
                    <p>全员参与，各部门协同使用不同工具</p>
                </div>
            </div>
        </div>

        <div class="cta-section">
            <h2>🚀 立即行动，抢占先机！</h2>
            <p>数字化转型不仅提升工作效率，更能获得丰厚资助回报</p>
            <p><strong>关键提醒：</strong>资助金额与工具使用数据正相关，使用量越大，资助越多！</p>
            <div class="cta-buttons">
                <a href="operation-guide.html" class="btn">查看操作指南</a>
                <a href="#" class="btn" onclick="showContactInfo()">立即咨询</a>
                <a href="#" class="btn" onclick="downloadMaterials()">下载申报材料</a>
            </div>
        </div>
    </div>

    <script>
        function showOperationGuide() {
            alert('操作指南页面即将开放，敬请期待！');
        }
        
        function showContactInfo() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); display: flex; align-items: center;
                justify-content: center; z-index: 1000;
            `;
            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%;">
                    <h3 style="color: #667eea; margin-bottom: 20px;">📞 联系我们</h3>
                    <p><strong>📧 邮件咨询：</strong></p>
                    <p>主邮箱：<EMAIL></p>
                    <p>抄送：<EMAIL></p>
                    <br>
                    <p><strong>📋 在线Q&A：</strong></p>
                    <p><a href="https://docs.qq.com/sheet/DQmJoampLZGpOd2dX?tab=BB08J2" target="_blank">腾讯文档数字化升级资助Q&A</a></p>
                    <br>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function downloadMaterials() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); display: flex; align-items: center;
                justify-content: center; z-index: 1000;
            `;
            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 600px; width: 90%;">
                    <h3 style="color: #667eea; margin-bottom: 20px;">📋 申报材料清单</h3>
                    <div style="text-align: left;">
                        <h4>必需材料：</h4>
                        <ul>
                            <li>机构证书（营业执照/登记证书）</li>
                            <li>评级证书（3A级优先）</li>
                            <li>捐赠协议（已提供模板）</li>
                            <li>预算表（已提供模板）</li>
                        </ul>
                        <br>
                        <h4>提交方式：</h4>
                        <p>邮件发送至：<EMAIL></p>
                        <p>抄送：<EMAIL></p>
                        <p>邮件标题：【机构名称】+【善育50申请材料】</p>
                        <br>
                        <p><strong>⏰ 申请截止：2025年8月15日</strong></p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                        关闭
                    </button>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        // 添加滚动动画效果
        window.addEventListener('scroll', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                const cardTop = card.getBoundingClientRect().top;
                const cardVisible = 150;
                
                if (cardTop < window.innerHeight - cardVisible) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });
        });
        
        // 初始化卡片动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            });
        });
    </script>
</body>
</html>
