<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字化工具操作指南 - 善育50·数字赋能计划</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .nav-tab {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
        }

        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }

        .tool-icon {
            font-size: 2.5em;
            margin-right: 15px;
        }

        .tool-title {
            flex: 1;
        }

        .tool-title h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .tool-subtitle {
            color: #666;
            font-size: 1.1em;
        }

        .step-section {
            margin: 25px 0;
        }

        .step-section h3 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 15px;
            padding-left: 20px;
            border-left: 4px solid #28a745;
        }

        .steps-list {
            list-style: none;
            padding: 0;
        }

        .step-item {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            position: relative;
        }

        .step-number {
            display: inline-block;
            background: #667eea;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: bold;
            margin-right: 10px;
            font-size: 0.9em;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-item {
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            color: white;
        }

        .feature-item h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .warning-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }

        .warning-box h4 {
            color: #721c24;
            margin-bottom: 10px;
        }

        .tip-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #2c3e50;
        }

        .tip-box h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .quick-start {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .quick-start h2 {
            color: #d63384;
            margin-bottom: 20px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .quick-action {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: transform 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quick-action:hover {
            transform: translateY(-5px);
            text-decoration: none;
            color: #333;
        }

        .quick-action h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .tool-header {
                flex-direction: column;
                text-align: center;
            }
            
            .tool-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 数字化工具操作指南</h1>
            <p class="subtitle">快速上手，轻松获得资助</p>
        </div>

        <div class="quick-start">
            <h2>🚀 快速开始</h2>
            <p>选择您要使用的工具，按照步骤操作，开始您的数字化之旅！</p>
            <div class="quick-actions">
                <a href="#" class="quick-action" onclick="showTab('funding')">
                    <h4>💰 资金拨付</h4>
                    <p>最高2.5万元资助</p>
                </a>
                <a href="#" class="quick-action" onclick="showTab('material')">
                    <h4>📦 物资执行</h4>
                    <p>最高8万元资助</p>
                </a>
                <a href="#" class="quick-action" onclick="showTab('service')">
                    <h4>🎯 服务执行</h4>
                    <p>最高5万元资助</p>
                </a>
                <a href="#" class="quick-action" onclick="showTab('operation')">
                    <h4>📢 私域运营</h4>
                    <p>最高2.4万元资助</p>
                </a>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('funding')">💰 资金拨付工具</button>
            <button class="nav-tab" onclick="showTab('material')">📦 物资执行工具</button>
            <button class="nav-tab" onclick="showTab('service')">🎯 服务执行工具</button>
            <button class="nav-tab" onclick="showTab('operation')">📢 私域运营工具</button>
        </div>

        <!-- 资金拨付工具 -->
        <div id="funding" class="tab-content active">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">💰</div>
                    <div class="tool-title">
                        <h2>资金拨付工具</h2>
                        <p class="tool-subtitle">通过移动支付方式进行公益资助拨款</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>移动支付拨款</h4>
                        <p>支持微信、支付宝等移动支付方式</p>
                    </div>
                    <div class="feature-item">
                        <h4>实时到账</h4>
                        <p>受益人可实时领取资助款项</p>
                    </div>
                    <div class="feature-item">
                        <h4>数据透明</h4>
                        <p>自动记录拨付数据，资金流向透明</p>
                    </div>
                    <div class="feature-item">
                        <h4>批量操作</h4>
                        <p>支持批量发放，提高工作效率</p>
                    </div>
                </div>

                <div class="step-section">
                    <h3>📋 操作步骤</h3>
                    <ol class="steps-list">
                        <li class="step-item">
                            <span class="step-number">1</span>
                            <strong>登录腾讯公益机构服务平台</strong><br>
                            使用机构账号登录，确保有相应权限
                        </li>
                        <li class="step-item">
                            <span class="step-number">2</span>
                            <strong>创建资金拨付项目</strong><br>
                            在项目管理中创建教育助学类项目
                        </li>
                        <li class="step-item">
                            <span class="step-number">3</span>
                            <strong>导入受益人信息</strong><br>
                            上传受益人名单，包含姓名、手机号等信息
                        </li>
                        <li class="step-item">
                            <span class="step-number">4</span>
                            <strong>设置拨付金额</strong><br>
                            为每个受益人设置拨付金额（≥10元）
                        </li>
                        <li class="step-item">
                            <span class="step-number">5</span>
                            <strong>发起拨付</strong><br>
                            确认信息无误后，发起资金拨付
                        </li>
                        <li class="step-item">
                            <span class="step-number">6</span>
                            <strong>跟踪拨付状态</strong><br>
                            实时查看拨付进度和受益人领取情况
                        </li>
                    </ol>
                </div>

                <div class="warning-box">
                    <h4>⚠️ 重要提醒</h4>
                    <ul>
                        <li>单个受益人拨款金额必须≥10元</li>
                        <li>受益人必须完成拨款领取才计入有效数据</li>
                        <li>仅限教育助学类项目使用</li>
                        <li>机构内部转账不计入资助范围</li>
                    </ul>
                </div>

                <div class="tip-box">
                    <h4>💡 获得资助</h4>
                    <p><strong>计算公式：</strong>有效受益人数 × 10元</p>
                    <p><strong>最高金额：</strong>25,000元</p>
                    <p><strong>示例：</strong>为100名学生发放助学金，每人200元 → 获得1,000元运营资助</p>
                </div>
            </div>
        </div>

        <!-- 物资执行工具 -->
        <div id="material" class="tab-content">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📦</div>
                    <div class="tool-title">
                        <h2>物资执行工具</h2>
                        <p class="tool-subtitle">仓配一体化物资发放管理工具</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>平台入仓</h4>
                        <p>统一仓储管理，规范化操作</p>
                    </div>
                    <div class="feature-item">
                        <h4>配送服务</h4>
                        <p>全国配送网络，快速到达</p>
                    </div>
                    <div class="feature-item">
                        <h4>轨迹跟踪</h4>
                        <p>实时物流信息，全程可追溯</p>
                    </div>
                    <div class="feature-item">
                        <h4>成本核算</h4>
                        <p>精确费用统计，透明化管理</p>
                    </div>
                </div>

                <div class="step-section">
                    <h3>📋 操作步骤</h3>
                    <ol class="steps-list">
                        <li class="step-item">
                            <span class="step-number">1</span>
                            <strong>物资准备</strong><br>
                            准备需要发放的公益物资（图书、学习用品等）
                        </li>
                        <li class="step-item">
                            <span class="step-number">2</span>
                            <strong>平台入仓</strong><br>
                            将物资送至指定仓库，完成入仓登记
                        </li>
                        <li class="step-item">
                            <span class="step-number">3</span>
                            <strong>收件人信息</strong><br>
                            上传受助对象、志愿者或捐赠人的收件信息
                        </li>
                        <li class="step-item">
                            <span class="step-number">4</span>
                            <strong>发起配送</strong><br>
                            在平台上创建配送任务，选择配送方式
                        </li>
                        <li class="step-item">
                            <span class="step-number">5</span>
                            <strong>跟踪配送</strong><br>
                            实时查看配送进度和签收状态
                        </li>
                        <li class="step-item">
                            <span class="step-number">6</span>
                            <strong>费用结算</strong><br>
                            查看物流费用明细，保存发票凭证
                        </li>
                    </ol>
                </div>

                <div class="warning-box">
                    <h4>⚠️ 重要提醒</h4>
                    <ul>
                        <li>物资必须通过平台入仓并配送</li>
                        <li>上门取件模式不纳入资助范围</li>
                        <li>机构内部物品物流费用不计入资助</li>
                        <li>需保留完整的发票和结算明细</li>
                    </ul>
                </div>

                <div class="tip-box">
                    <h4>💡 获得资助</h4>
                    <p><strong>计算公式：</strong>实际物流费用100%报销</p>
                    <p><strong>最高金额：</strong>80,000元</p>
                    <p><strong>示例：</strong>为500名学生配送学习用品，物流费3,000元 → 获得3,000元资助</p>
                </div>
            </div>
        </div>

        <!-- 服务执行工具 -->
        <div id="service" class="tab-content">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🎯</div>
                    <div class="tool-title">
                        <h2>服务执行工具</h2>
                        <p class="tool-subtitle">标准化服务任务管理工具</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>计件化管理</h4>
                        <p>将服务拆解为最小执行单位</p>
                    </div>
                    <div class="feature-item">
                        <h4>任务跟踪</h4>
                        <p>实时监控任务执行进度</p>
                    </div>
                    <div class="feature-item">
                        <h4>过程记录</h4>
                        <p>自动留存执行过程数据</p>
                    </div>
                    <div class="feature-item">
                        <h4>实时反馈</h4>
                        <p>即时获得服务执行结果</p>
                    </div>
                </div>

                <div class="step-section">
                    <h3>📋 操作步骤</h3>
                    <ol class="steps-list">
                        <li class="step-item">
                            <span class="step-number">1</span>
                            <strong>安装服务工具</strong><br>
                            在企微后台安装服务执行工具应用
                        </li>
                        <li class="step-item">
                            <span class="step-number">2</span>
                            <strong>创建服务任务</strong><br>
                            定义服务类型（课堂、家访、陪伴等）
                        </li>
                        <li class="step-item">
                            <span class="step-number">3</span>
                            <strong>设置服务对象</strong><br>
                            添加受助对象信息，设置服务参数
                        </li>
                        <li class="step-item">
                            <span class="step-number">4</span>
                            <strong>分配执行人员</strong><br>
                            将任务分配给具体的执行人员
                        </li>
                        <li class="step-item">
                            <span class="step-number">5</span>
                            <strong>执行服务</strong><br>
                            执行人员通过小程序完成服务任务
                        </li>
                        <li class="step-item">
                            <span class="step-number">6</span>
                            <strong>确认完成</strong><br>
                            上传服务证明，确认任务完成状态
                        </li>
                    </ol>
                </div>

                <div class="warning-box">
                    <h4>⚠️ 重要提醒</h4>
                    <ul>
                        <li>有效任务需是完成状态</li>
                        <li>衡量周期内对服务对象去重</li>
                        <li>需要真实的服务执行记录</li>
                        <li>支持多种服务场景类型</li>
                    </ul>
                </div>

                <div class="tip-box">
                    <h4>💡 获得资助</h4>
                    <p><strong>计算公式：</strong>资助系数 × 受助对象数量 × 5元</p>
                    <p><strong>最高金额：</strong>50,000元</p>
                    <p><strong>系数计算：</strong>服务场景(1) + 人群系数(个人0.3/群体0.5) + 场景系数(线下0.3/线上0.1)</p>
                    <p><strong>示例：</strong>线上课堂教学，群体40人 → (1+0.5+0.1)×40×5=320元</p>
                </div>
            </div>
        </div>

        <!-- 私域运营工具 -->
        <div id="operation" class="tab-content">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📢</div>
                    <div class="tool-title">
                        <h2>私域运营工具</h2>
                        <p class="tool-subtitle">企微私域运营和腾讯频道运营工具</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>企微私域</h4>
                        <p>用户管理、群发消息、朋友圈运营</p>
                    </div>
                    <div class="feature-item">
                        <h4>腾讯频道</h4>
                        <p>内容沉淀、多角色交流互动</p>
                    </div>
                    <div class="feature-item">
                        <h4>捐赠人服务</h4>
                        <p>月捐用户管理、触达服务</p>
                    </div>
                    <div class="feature-item">
                        <h4>数据分析</h4>
                        <p>运营数据统计、效果分析</p>
                    </div>
                </div>

                <div class="step-section">
                    <h3>📋 企微私域操作步骤</h3>
                    <ol class="steps-list">
                        <li class="step-item">
                            <span class="step-number">1</span>
                            <strong>企微创建及设置</strong><br>
                            创建企业微信，完成基础配置
                        </li>
                        <li class="step-item">
                            <span class="step-number">2</span>
                            <strong>安装运营工具</strong><br>
                            在企微后台安装私域运营工具
                        </li>
                        <li class="step-item">
                            <span class="step-number">3</span>
                            <strong>组织认证</strong><br>
                            完成组织认证，建立组织通讯录
                        </li>
                        <li class="step-item">
                            <span class="step-number">4</span>
                            <strong>用户管理</strong><br>
                            设置用户标签，管理外部联系人
                        </li>
                        <li class="step-item">
                            <span class="step-number">5</span>
                            <strong>内容运营</strong><br>
                            每月至少1次活动，4次素材发布
                        </li>
                    </ol>
                </div>

                <div class="step-section">
                    <h3>📋 腾讯频道操作步骤</h3>
                    <ol class="steps-list">
                        <li class="step-item">
                            <span class="step-number">1</span>
                            <strong>创建频道</strong><br>
                            创建公益项目或机构运营频道
                        </li>
                        <li class="step-item">
                            <span class="step-number">2</span>
                            <strong>邀请用户</strong><br>
                            邀请捐赠人、志愿者等加入频道
                        </li>
                        <li class="step-item">
                            <span class="step-number">3</span>
                            <strong>内容发布</strong><br>
                            每周至少3条发帖，分享项目进展
                        </li>
                        <li class="step-item">
                            <span class="step-number">4</span>
                            <strong>互动管理</strong><br>
                            鼓励用户参与互动，提升活跃度
                        </li>
                    </ol>
                </div>

                <div class="warning-box">
                    <h4>⚠️ 重要提醒</h4>
                    <ul>
                        <li>频道人数需超过100人</li>
                        <li>发帖人数占比需超过10%</li>
                        <li>平均每帖互动数需超过5个</li>
                        <li>需要配置专门的运营人员</li>
                    </ul>
                </div>

                <div class="tip-box">
                    <h4>💡 获得资助</h4>
                    <p><strong>资助形式：</strong>运营人员费用</p>
                    <p><strong>资助标准：</strong>4000元/月</p>
                    <p><strong>最高金额：</strong>24,000元（6个月）</p>
                    <p><strong>要求：</strong>企微私域和频道运营月度数据均达标</p>
                </div>
            </div>
        </div>
    </div>

        <!-- FAQ部分 -->
        <div class="tool-card">
            <div class="tool-header">
                <div class="tool-icon">❓</div>
                <div class="tool-title">
                    <h2>常见问题解答</h2>
                    <p class="tool-subtitle">快速解决使用过程中的疑问</p>
                </div>
            </div>

            <div class="faq-section">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>Q: 如何确保获得最大资助金额？</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>A: 关键在于最大化工具使用数据：</p>
                        <ul>
                            <li>资金拨付：增加有效受益人数，确保每人≥10元</li>
                            <li>物资执行：多使用平台配送服务，保留完整发票</li>
                            <li>服务执行：提高服务频次，选择高系数的服务类型</li>
                            <li>私域运营：保持活跃度，达到所有考核指标</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>Q: 工具使用有什么前置条件？</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>A: 基本条件包括：</p>
                        <ul>
                            <li>具有公募资质的教育类公益机构</li>
                            <li>已入驻腾讯公益平台</li>
                            <li>有稳定的项目执行团队</li>
                            <li>积极推动数字化发展的意愿</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>Q: 资助金额什么时候到账？</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>A: 资助拨付流程：</p>
                        <ul>
                            <li>项目执行期：2025年3月1日-8月31日</li>
                            <li>材料提交：项目结束后10日内</li>
                            <li>审核验收：材料审核通过后</li>
                            <li>资助拨付：验收合格后及时拨付</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>Q: 可以同时使用多个工具吗？</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>A: 完全可以！建议策略：</p>
                        <ul>
                            <li>同时使用4个工具，最高可获得17.9万元资助</li>
                            <li>不同工具适用不同业务场景，可以并行使用</li>
                            <li>合理规划，让不同部门负责不同工具</li>
                            <li>数据不重复计算，各工具独立核算</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>Q: 遇到技术问题怎么办？</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>A: 多种支持渠道：</p>
                        <ul>
                            <li>邮件咨询：<EMAIL>（抄送：<EMAIL>）</li>
                            <li>腾讯文档Q&A：填写问题获得解答</li>
                            <li>平台客服：腾讯公益机构服务平台反馈功能</li>
                            <li>技术支持：工具使用过程中的在线帮助</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系我们 -->
        <div class="tool-card">
            <div class="tool-header">
                <div class="tool-icon">📞</div>
                <div class="tool-title">
                    <h2>联系我们</h2>
                    <p class="tool-subtitle">获得更多帮助和支持</p>
                </div>
            </div>

            <div class="contact-grid">
                <div class="contact-item">
                    <h4>📧 邮件咨询</h4>
                    <p><EMAIL></p>
                    <p>抄送：<EMAIL></p>
                </div>
                <div class="contact-item">
                    <h4>📋 在线Q&A</h4>
                    <p>腾讯文档数字化升级资助Q&A</p>
                    <a href="https://docs.qq.com/sheet/DQmJoampLZGpOd2dX?tab=BB08J2" target="_blank">点击访问</a>
                </div>
                <div class="contact-item">
                    <h4>⏰ 申请时间</h4>
                    <p>即日起至2025年8月15日</p>
                    <p>滚动申请，先到先得</p>
                </div>
                <div class="contact-item">
                    <h4>🎯 执行周期</h4>
                    <p>2025年3月1日-8月31日</p>
                    <p>越早开始，数据越多</p>
                </div>
            </div>
        </div>
    </div>

    <style>
        .faq-section {
            margin: 20px 0;
        }

        .faq-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin: 10px 0;
            overflow: hidden;
        }

        .faq-question {
            background: #f8f9fa;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }

        .faq-question:hover {
            background: #e9ecef;
        }

        .faq-question h4 {
            margin: 0;
            color: #333;
        }

        .faq-toggle {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 15px 20px;
            max-height: 500px;
        }

        .faq-answer ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .faq-answer li {
            margin: 5px 0;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .contact-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .contact-item h4 {
            margin-bottom: 10px;
        }

        .contact-item a {
            color: white;
            text-decoration: underline;
        }

        .contact-item a:hover {
            color: #ffd700;
        }
    </style>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));

            // 移除所有标签页按钮的激活状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 激活对应的标签页按钮
            const activeTab = Array.from(tabs).find(tab =>
                tab.textContent.includes(getTabDisplayName(tabName))
            );
            if (activeTab) {
                activeTab.classList.add('active');
            }
        }

        function getTabDisplayName(tabName) {
            const names = {
                'funding': '资金拨付',
                'material': '物资执行',
                'service': '服务执行',
                'operation': '私域运营'
            };
            return names[tabName] || tabName;
        }

        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const toggle = element.querySelector('.faq-toggle');

            if (answer.classList.contains('active')) {
                answer.classList.remove('active');
                toggle.textContent = '+';
            } else {
                // 关闭其他FAQ
                document.querySelectorAll('.faq-answer.active').forEach(item => {
                    item.classList.remove('active');
                    item.previousElementSibling.querySelector('.faq-toggle').textContent = '+';
                });

                // 打开当前FAQ
                answer.classList.add('active');
                toggle.textContent = '-';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加平滑滚动效果
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
